#!/usr/bin/env python3
"""
IP Tools - Enhanced Network Analysis Toolkit
Version 2.0

A comprehensive toolkit for IP address analysis, DNS lookups, WHOIS queries,
and network reconnaissance with improved error handling and validation.
"""

import sys
import os
from typing import Dict, Any, Optional
from utils import (
    ip_lookup, whois_lookup, dns_lookup, reverse_dns,
    check_blacklist, hostname_lookup, generate_random_ip, run_api,
    logger
)
from advanced_tools import (
    get_ip_geolocation, get_ip_reputation, analyze_ip_comprehensive,
    subnet_analyzer
)
from dns_tools import (
    dns_over_https, dns_security_check, dns_trace, bulk_dns_lookup
)
from config import setup_logging, get_config

class IPToolsInterface:
    """Enhanced command-line interface for IP Tools."""

    def __init__(self):
        self.config = get_config()
        self.menu_options = {
            '1': ('IP Address Lookup', self.handle_ip_lookup),
            '2': ('IP WHOIS Lookup', self.handle_whois_lookup),
            '3': ('DNS Lookup', self.handle_dns_lookup),
            '4': ('Reverse DNS Lookup', self.handle_reverse_dns),
            '5': ('IP Blacklist Check', self.handle_blacklist_check),
            '6': ('Hostname Lookup', self.handle_hostname_lookup),
            '7': ('Random IP Generator', self.handle_random_ip),
            '8': ('Run API Server', self.handle_api_server),
            '9': ('Batch Operations', self.handle_batch_operations),
            'a': ('Advanced IP Analysis', self.handle_advanced_analysis),
            'b': ('DNS Security Check', self.handle_dns_security),
            'c': ('DNS over HTTPS', self.handle_dns_over_https),
            'd': ('Subnet Analysis', self.handle_subnet_analysis),
            'e': ('Geolocation Lookup', self.handle_geolocation),
            '0': ('Exit', self.handle_exit)
        }

    def display_banner(self):
        """Display application banner."""
        print("=" * 60)
        print("🌐 IP Tools - Enhanced Network Analysis Toolkit v2.0")
        print("=" * 60)
        print("Features: IP Lookup, WHOIS, DNS, Blacklist Check, API Server")
        print("-" * 60)

    def display_menu(self):
        """Display the main menu."""
        print("\n📋 Available Functions:")
        for key, (description, _) in self.menu_options.items():
            print(f"   {key}. {description}")
        print()

    def get_user_choice(self) -> str:
        """Get and validate user choice."""
        while True:
            choice = input("Choose a function (0-9, a-e): ").strip().lower()
            if choice in self.menu_options:
                return choice
            print("❌ Invalid choice. Please select a valid option.")

    def handle_ip_lookup(self):
        """Handle IP address lookup."""
        ip = input("Enter IP address: ").strip()
        if ip:
            result = ip_lookup(ip)
            self.save_result_option(result, f"ip_lookup_{ip}")

    def handle_whois_lookup(self):
        """Handle WHOIS lookup."""
        ip = input("Enter IP address: ").strip()
        if ip:
            result = whois_lookup(ip)
            self.save_result_option(result, f"whois_{ip}")

    def handle_dns_lookup(self):
        """Handle DNS lookup with record type selection."""
        domain = input("Enter domain name: ").strip()
        if domain:
            print("DNS Record Types: A, AAAA, MX, CNAME, TXT, NS, SOA")
            record_type = input("Enter record type (default: A): ").strip() or "A"
            result = dns_lookup(domain, record_type)
            self.save_result_option(result, f"dns_{domain}_{record_type}")

    def handle_reverse_dns(self):
        """Handle reverse DNS lookup."""
        ip = input("Enter IP address: ").strip()
        if ip:
            result = reverse_dns(ip)
            self.save_result_option(result, f"reverse_dns_{ip}")

    def handle_blacklist_check(self):
        """Handle blacklist checking."""
        ip = input("Enter IP address: ").strip()
        if ip:
            result = check_blacklist(ip)
            self.save_result_option(result, f"blacklist_{ip}")

    def handle_hostname_lookup(self):
        """Handle hostname lookup."""
        ip = input("Enter IP address: ").strip()
        if ip:
            result = hostname_lookup(ip)
            self.save_result_option(result, f"hostname_{ip}")

    def handle_random_ip(self):
        """Handle random IP generation."""
        try:
            count = int(input("Number of IPs to generate (default: 1): ").strip() or "1")
            ip_type = input("IP type (public/private, default: public): ").strip() or "public"
            result = generate_random_ip(count, ip_type)
            self.save_result_option(result, f"random_ip_{count}_{ip_type}")
        except ValueError:
            print("❌ Invalid number format.")

    def handle_api_server(self):
        """Handle API server startup."""
        print("🚀 Starting API Server...")
        api_config = self.config.get_api_config()

        host = input(f"Host (default: {api_config.get('host', '127.0.0.1')}): ").strip() or api_config.get('host', '127.0.0.1')
        try:
            port = int(input(f"Port (default: {api_config.get('port', 5000)}): ").strip() or str(api_config.get('port', 5000)))
            debug = input(f"Debug mode? (default: {api_config.get('debug', False)}) (y/N): ").strip().lower() == 'y'
            run_api(host, port, debug)
        except ValueError:
            print("❌ Invalid port number.")
        except KeyboardInterrupt:
            print("\n🛑 API server stopped.")

    def handle_batch_operations(self):
        """Handle batch operations from file."""
        filename = input("Enter filename with IP addresses (one per line): ").strip()
        if not filename:
            return

        try:
            with open(filename, 'r') as f:
                ips = [line.strip() for line in f if line.strip()]

            print(f"📁 Processing {len(ips)} IP addresses...")
            results = []

            for i, ip in enumerate(ips, 1):
                print(f"Processing {i}/{len(ips)}: {ip}")
                result = ip_lookup(ip)
                results.append(result)

            # Save batch results
            import json
            output_file = f"batch_results_{len(ips)}_ips.json"
            with open(output_file, 'w') as f:
                json.dump(results, f, indent=2)

            print(f"✅ Batch processing complete. Results saved to {output_file}")

        except FileNotFoundError:
            print(f"❌ File '{filename}' not found.")
        except Exception as e:
            print(f"❌ Error processing batch: {e}")

    def save_result_option(self, result: Dict[str, Any], filename_prefix: str):
        """Offer to save results to file."""
        if input("\n💾 Save results to file? (y/N): ").strip().lower() == 'y':
            try:
                import json
                filename = f"{filename_prefix}_{result.get('timestamp', 'unknown').replace(':', '-')}.json"
                with open(filename, 'w') as f:
                    json.dump(result, f, indent=2)
                print(f"✅ Results saved to {filename}")
            except Exception as e:
                print(f"❌ Error saving file: {e}")

    def handle_advanced_analysis(self):
        """Handle comprehensive IP analysis."""
        ip = input("Enter IP address for comprehensive analysis: ").strip()
        if ip:
            result = analyze_ip_comprehensive(ip)
            self.save_result_option(result, f"comprehensive_analysis_{ip}")

    def handle_dns_security(self):
        """Handle DNS security check."""
        domain = input("Enter domain for security check: ").strip()
        if domain:
            result = dns_security_check(domain)
            self.save_result_option(result, f"dns_security_{domain}")

    def handle_dns_over_https(self):
        """Handle DNS over HTTPS lookup."""
        domain = input("Enter domain name: ").strip()
        if domain:
            print("DNS Record Types: A, AAAA, MX, CNAME, TXT, NS, SOA")
            record_type = input("Enter record type (default: A): ").strip() or "A"
            print("DoH Servers: cloudflare, google, quad9")
            doh_server = input("Enter DoH server (default: cloudflare): ").strip() or "cloudflare"
            result = dns_over_https(domain, record_type, doh_server)
            self.save_result_option(result, f"doh_{domain}_{record_type}_{doh_server}")

    def handle_subnet_analysis(self):
        """Handle subnet analysis."""
        cidr = input("Enter CIDR notation (e.g., ***********/24): ").strip()
        if cidr:
            result = subnet_analyzer(cidr)
            self.save_result_option(result, f"subnet_analysis_{cidr.replace('/', '_')}")

    def handle_geolocation(self):
        """Handle IP geolocation lookup."""
        ip = input("Enter IP address for geolocation: ").strip()
        if ip:
            result = get_ip_geolocation(ip)
            self.save_result_option(result, f"geolocation_{ip}")

    def handle_exit(self):
        """Handle application exit."""
        print("👋 Thank you for using IP Tools!")
        sys.exit(0)

    def run(self):
        """Run the main application loop."""
        try:
            self.display_banner()

            while True:
                self.display_menu()
                choice = self.get_user_choice()

                _, handler = self.menu_options[choice]
                try:
                    handler()
                except KeyboardInterrupt:
                    print("\n⚠️  Operation cancelled by user.")
                except Exception as e:
                    logger.error(f"Error in {handler.__name__}: {e}")
                    print(f"❌ An error occurred: {e}")

                if choice != '8':  # Don't pause after API server
                    input("\nPress Enter to continue...")

        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            sys.exit(0)

def main():
    """Main entry point."""
    # Setup logging and configuration
    setup_logging()

    # Initialize and run the application
    app = IPToolsInterface()
    app.run()

if __name__ == "__main__":
    main()
