#!/usr/bin/env python3
"""
Comprehensive Test Suite for IP Tools
Version 2.0

Unit tests and integration tests for all IP Tools functionality.
"""

import pytest
import json
import tempfile
import os
from unittest.mock import patch, MagicMock
from pathlib import Path

# Import modules to test
import utils
import advanced_tools
import dns_tools
import config

class TestInputValidation:
    """Test input validation functions."""
    
    def test_validate_ip_address_valid(self):
        """Test valid IP addresses."""
        assert utils.validate_ip_address("***********") == True
        assert utils.validate_ip_address("*******") == True
        assert utils.validate_ip_address("127.0.0.1") == True
        assert utils.validate_ip_address("::1") == True
        assert utils.validate_ip_address("2001:db8::1") == True
    
    def test_validate_ip_address_invalid(self):
        """Test invalid IP addresses."""
        assert utils.validate_ip_address("256.256.256.256") == False
        assert utils.validate_ip_address("192.168.1") == False
        assert utils.validate_ip_address("not.an.ip") == False
        assert utils.validate_ip_address("") == False
        assert utils.validate_ip_address("***********.1") == False
    
    def test_validate_domain_valid(self):
        """Test valid domain names."""
        assert utils.validate_domain("example.com") == True
        assert utils.validate_domain("sub.example.com") == True
        assert utils.validate_domain("test-domain.org") == True
        assert utils.validate_domain("a.b.c.d.example.com") == True
    
    def test_validate_domain_invalid(self):
        """Test invalid domain names."""
        assert utils.validate_domain("") == False
        assert utils.validate_domain("a" * 254) == False
        assert utils.validate_domain(".example.com") == False
        assert utils.validate_domain("example..com") == False
        assert utils.validate_domain("example.com.") == True  # Trailing dot is valid
    
    def test_sanitize_input(self):
        """Test input sanitization."""
        assert utils.sanitize_input("  test  ") == "test"
        assert utils.sanitize_input("test;rm -rf /") == "testrm -rf /"
        assert utils.sanitize_input("test&echo hello") == "testecho hello"
        assert utils.sanitize_input("test|cat /etc/passwd") == "testcat /etc/passwd"

class TestIPLookup:
    """Test IP lookup functionality."""
    
    @patch('socket.gethostbyaddr')
    def test_ip_lookup_success(self, mock_gethostbyaddr):
        """Test successful IP lookup."""
        mock_gethostbyaddr.return_value = ("example.com", [], ["*******"])
        
        result = utils.ip_lookup("*******")
        
        assert result["success"] == True
        assert result["ip"] == "*******"
        assert result["hostname"] == "example.com"
        assert "timestamp" in result
    
    def test_ip_lookup_invalid_ip(self):
        """Test IP lookup with invalid IP."""
        result = utils.ip_lookup("invalid.ip")
        
        assert result["success"] == False
        assert "error" in result
        assert "Invalid IP address" in result["error"]
    
    @patch('socket.gethostbyaddr')
    def test_ip_lookup_host_not_found(self, mock_gethostbyaddr):
        """Test IP lookup when host is not found."""
        import socket
        mock_gethostbyaddr.side_effect = socket.herror("Host not found")
        
        result = utils.ip_lookup("***********")
        
        assert result["success"] == False
        assert "Host not found" in result["error"]

class TestDNSLookup:
    """Test DNS lookup functionality."""
    
    @patch('dns.resolver.resolve')
    def test_dns_lookup_success(self, mock_resolve):
        """Test successful DNS lookup."""
        mock_record = MagicMock()
        mock_record.to_text.return_value = "*************"
        mock_response = MagicMock()
        mock_response.__iter__ = lambda x: iter([mock_record])
        mock_response.ttl = 300
        mock_resolve.return_value = mock_response
        
        result = utils.dns_lookup("example.com", "A")
        
        assert result["success"] == True
        assert result["domain"] == "example.com"
        assert result["record_type"] == "A"
        assert result["ttl"] == 300
        assert "*************" in result["records"]
    
    def test_dns_lookup_invalid_domain(self):
        """Test DNS lookup with invalid domain."""
        result = utils.dns_lookup("", "A")
        
        assert result["success"] == False
        assert "Invalid domain" in result["error"]
    
    def test_dns_lookup_invalid_record_type(self):
        """Test DNS lookup with invalid record type."""
        result = utils.dns_lookup("example.com", "INVALID")
        
        assert result["success"] == False
        assert "Invalid DNS record type" in result["error"]

class TestRandomIPGenerator:
    """Test random IP generation."""
    
    def test_generate_random_ip_public(self):
        """Test public IP generation."""
        result = utils.generate_random_ip(5, "public")
        
        assert result["success"] == True
        assert result["count"] == 5
        assert result["type"] == "public"
        assert len(result["generated_ips"]) == 5
        
        # Check that all IPs are valid
        for ip in result["generated_ips"]:
            assert utils.validate_ip_address(ip) == True
    
    def test_generate_random_ip_private(self):
        """Test private IP generation."""
        result = utils.generate_random_ip(3, "private")
        
        assert result["success"] == True
        assert result["count"] == 3
        assert result["type"] == "private"
        assert len(result["generated_ips"]) == 3
        
        # Check that all IPs are valid and private
        import ipaddress
        for ip in result["generated_ips"]:
            assert utils.validate_ip_address(ip) == True
            ip_obj = ipaddress.ip_address(ip)
            assert ip_obj.is_private == True

class TestAdvancedTools:
    """Test advanced tools functionality."""
    
    @patch('requests.get')
    def test_get_ip_geolocation_success(self, mock_get):
        """Test successful geolocation lookup."""
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "ip": "*******",
            "city": "Mountain View",
            "region": "California",
            "country_name": "United States",
            "country_code": "US",
            "latitude": 37.4056,
            "longitude": -122.0775,
            "timezone": "America/Los_Angeles",
            "org": "Google LLC",
            "asn": "AS15169"
        }
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        result = advanced_tools.get_ip_geolocation("*******")
        
        assert result["success"] == True
        assert result["ip"] == "*******"
        assert result["geolocation"]["city"] == "Mountain View"
        assert result["geolocation"]["country"] == "United States"
    
    def test_get_ip_geolocation_invalid_ip(self):
        """Test geolocation with invalid IP."""
        result = advanced_tools.get_ip_geolocation("invalid.ip")
        
        assert result["success"] == False
        assert "Invalid IP address" in result["error"]
    
    def test_subnet_analyzer_valid(self):
        """Test subnet analysis with valid CIDR."""
        result = advanced_tools.subnet_analyzer("***********/24")
        
        assert result["success"] == True
        assert result["network_address"] == "***********"
        assert result["broadcast_address"] == "*************"
        assert result["num_addresses"] == 256
        assert result["num_hosts"] == 254
        assert result["is_private"] == True
    
    def test_subnet_analyzer_invalid(self):
        """Test subnet analysis with invalid CIDR."""
        result = advanced_tools.subnet_analyzer("invalid/cidr")
        
        assert result["success"] == False
        assert "Invalid CIDR notation" in result["error"]

class TestDNSTools:
    """Test DNS tools functionality."""
    
    @patch('requests.get')
    def test_dns_over_https_success(self, mock_get):
        """Test successful DNS over HTTPS lookup."""
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "Status": 0,
            "Answer": [
                {
                    "name": "example.com",
                    "type": 1,
                    "TTL": 300,
                    "data": "*************"
                }
            ]
        }
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        result = dns_tools.dns_over_https("example.com", "A", "cloudflare")
        
        assert result["success"] == True
        assert result["domain"] == "example.com"
        assert result["status"] == 0
        assert len(result["answers"]) == 1
        assert result["answers"][0]["data"] == "*************"
    
    def test_dns_over_https_invalid_domain(self):
        """Test DoH with invalid domain."""
        result = dns_tools.dns_over_https("", "A", "cloudflare")
        
        assert result["success"] == False
        assert "Invalid domain" in result["error"]
    
    def test_dns_over_https_invalid_server(self):
        """Test DoH with invalid server."""
        result = dns_tools.dns_over_https("example.com", "A", "invalid_server")
        
        assert result["success"] == False
        assert "Invalid DoH server" in result["error"]

class TestConfiguration:
    """Test configuration management."""
    
    def test_config_manager_initialization(self):
        """Test configuration manager initialization."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = os.path.join(temp_dir, "test_config.json")
            config_manager = config.ConfigManager(config_file)
            
            assert config_manager.config is not None
            assert "api" in config_manager.config
            assert "dns" in config_manager.config
            assert "logging" in config_manager.config
    
    def test_config_get_set(self):
        """Test configuration get and set methods."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = os.path.join(temp_dir, "test_config.json")
            config_manager = config.ConfigManager(config_file)
            
            # Test get
            assert config_manager.get("api.host") == "127.0.0.1"
            assert config_manager.get("nonexistent.key", "default") == "default"
            
            # Test set
            config_manager.set("api.host", "0.0.0.0")
            assert config_manager.get("api.host") == "0.0.0.0"
    
    def test_config_validation(self):
        """Test configuration validation."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = os.path.join(temp_dir, "test_config.json")
            config_manager = config.ConfigManager(config_file)
            
            validation = config_manager.validate_config()
            assert "valid" in validation
            assert "errors" in validation
            assert "warnings" in validation
    
    def test_config_save_load(self):
        """Test configuration save and load."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = os.path.join(temp_dir, "test_config.json")
            config_manager = config.ConfigManager(config_file)
            
            # Modify config
            config_manager.set("api.port", 8080)
            
            # Save config
            assert config_manager.save_config() == True
            assert os.path.exists(config_file)
            
            # Create new instance and load
            new_config_manager = config.ConfigManager(config_file)
            assert new_config_manager.get("api.port") == 8080

class TestIntegration:
    """Integration tests."""
    
    def test_comprehensive_ip_analysis_invalid_ip(self):
        """Test comprehensive analysis with invalid IP."""
        result = advanced_tools.analyze_ip_comprehensive("invalid.ip")
        
        assert result["success"] == False
        assert "Invalid IP address" in result["error"]
    
    @patch('utils.ip_lookup')
    @patch('utils.whois_lookup')
    @patch('utils.reverse_dns')
    @patch('advanced_tools.get_ip_geolocation')
    @patch('advanced_tools.get_ip_reputation')
    def test_comprehensive_ip_analysis_success(self, mock_reputation, mock_geo, 
                                             mock_reverse, mock_whois, mock_lookup):
        """Test successful comprehensive analysis."""
        # Mock all the individual functions
        mock_lookup.return_value = {"success": True, "hostname": "example.com"}
        mock_whois.return_value = {"success": True, "network": {"name": "Test Network"}}
        mock_reverse.return_value = {"success": True, "hostname": "example.com"}
        mock_geo.return_value = {"success": True, "geolocation": {"city": "Test City"}}
        mock_reputation.return_value = {"success": True, "reputation_sources": []}
        
        result = advanced_tools.analyze_ip_comprehensive("*******")
        
        assert result["success"] == True
        assert "analysis" in result
        assert "basic_lookup" in result["analysis"]
        assert "whois" in result["analysis"]
        assert "reverse_dns" in result["analysis"]
        assert "geolocation" in result["analysis"]
        assert "reputation" in result["analysis"]

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v", "--tb=short"])
