import socket
import random
import requests
import ipaddress
import logging
import json
import time
from typing import Dict, List, Optional, Union, Any
from datetime import datetime
import dns.resolver
from ipwhois import IPWhois
from flask import Flask, request, jsonify

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ip_tools.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Input validation functions
def validate_ip_address(ip: str) -> bool:
    """Validate if the input is a valid IP address."""
    try:
        ipaddress.ip_address(ip)
        return True
    except ValueError:
        return False

def validate_domain(domain: str) -> bool:
    """Validate if the input is a valid domain name."""
    if not domain or len(domain) > 253:
        return False

    # Basic domain validation
    import re
    pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
    return bool(re.match(pattern, domain))

def sanitize_input(user_input: str) -> str:
    """Sanitize user input by removing dangerous characters."""
    if not user_input:
        return ""
    return user_input.strip().replace(';', '').replace('&', '').replace('|', '')

def handle_api_error(func):
    """Decorator for handling API errors gracefully."""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed: {e}")
            return {"error": f"API request failed: {str(e)}"}
        except Exception as e:
            logger.error(f"Unexpected error in {func.__name__}: {e}")
            return {"error": f"Unexpected error: {str(e)}"}
    return wrapper

def ip_lookup(ip: str) -> Dict[str, Any]:
    """Enhanced IP lookup with validation and detailed error handling."""
    ip = sanitize_input(ip)

    if not validate_ip_address(ip):
        error_msg = f"Invalid IP address format: {ip}"
        logger.error(error_msg)
        print(f"❌ {error_msg}")
        return {"error": error_msg, "ip": ip}

    try:
        logger.info(f"Performing IP lookup for: {ip}")
        hostname_info = socket.gethostbyaddr(ip)

        result = {
            "ip": ip,
            "hostname": hostname_info[0],
            "aliases": hostname_info[1],
            "addresses": hostname_info[2],
            "timestamp": datetime.now().isoformat(),
            "success": True
        }

        print(f"✅ IP Lookup Results for {ip}:")
        print(f"   Hostname: {result['hostname']}")
        if result['aliases']:
            print(f"   Aliases: {', '.join(result['aliases'])}")

        logger.info(f"IP lookup successful for {ip}")
        return result

    except socket.herror as e:
        error_msg = f"Host not found for IP {ip}: {str(e)}"
        logger.warning(error_msg)
        print(f"⚠️  {error_msg}")
        return {"error": error_msg, "ip": ip, "success": False}

    except socket.gaierror as e:
        error_msg = f"Address resolution error for IP {ip}: {str(e)}"
        logger.error(error_msg)
        print(f"❌ {error_msg}")
        return {"error": error_msg, "ip": ip, "success": False}

    except Exception as e:
        error_msg = f"Unexpected error during IP lookup for {ip}: {str(e)}"
        logger.error(error_msg)
        print(f"❌ {error_msg}")
        return {"error": error_msg, "ip": ip, "success": False}

def whois_lookup(ip: str) -> Dict[str, Any]:
    """Enhanced WHOIS lookup with validation and detailed information."""
    ip = sanitize_input(ip)

    if not validate_ip_address(ip):
        error_msg = f"Invalid IP address format: {ip}"
        logger.error(error_msg)
        print(f"❌ {error_msg}")
        return {"error": error_msg, "ip": ip}

    try:
        logger.info(f"Performing WHOIS lookup for: {ip}")
        obj = IPWhois(ip)
        results = obj.lookup_rdap()

        # Extract relevant information
        network_info = results.get('network', {})
        asn_info = results.get('asn', {})

        result = {
            "ip": ip,
            "network": {
                "cidr": network_info.get('cidr'),
                "name": network_info.get('name'),
                "handle": network_info.get('handle'),
                "start_address": network_info.get('start_address'),
                "end_address": network_info.get('end_address'),
                "country": network_info.get('country'),
                "type": network_info.get('type')
            },
            "asn": {
                "asn": asn_info.get('asn'),
                "description": asn_info.get('description'),
                "country_code": asn_info.get('country_code')
            },
            "timestamp": datetime.now().isoformat(),
            "success": True
        }

        print(f"✅ WHOIS Results for {ip}:")
        print(f"   Network: {result['network']['name']} ({result['network']['cidr']})")
        print(f"   ASN: AS{result['asn']['asn']} - {result['asn']['description']}")
        print(f"   Country: {result['network']['country']}")
        print(f"   Range: {result['network']['start_address']} - {result['network']['end_address']}")

        logger.info(f"WHOIS lookup successful for {ip}")
        return result

    except Exception as e:
        error_msg = f"WHOIS lookup failed for {ip}: {str(e)}"
        logger.error(error_msg)
        print(f"❌ {error_msg}")
        return {"error": error_msg, "ip": ip, "success": False}

def dns_lookup(domain: str, record_type: str = 'A') -> Dict[str, Any]:
    """Enhanced DNS lookup with support for multiple record types."""
    domain = sanitize_input(domain)

    if not validate_domain(domain):
        error_msg = f"Invalid domain format: {domain}"
        logger.error(error_msg)
        print(f"❌ {error_msg}")
        return {"error": error_msg, "domain": domain}

    valid_types = ['A', 'AAAA', 'MX', 'CNAME', 'TXT', 'NS', 'SOA', 'PTR']
    if record_type.upper() not in valid_types:
        error_msg = f"Invalid DNS record type: {record_type}. Valid types: {', '.join(valid_types)}"
        logger.error(error_msg)
        print(f"❌ {error_msg}")
        return {"error": error_msg, "domain": domain}

    try:
        logger.info(f"Performing DNS lookup for: {domain} (type: {record_type})")
        result = dns.resolver.resolve(domain, record_type.upper())

        records = []
        for record in result:
            if record_type.upper() == 'MX':
                records.append({
                    "priority": record.preference,
                    "exchange": str(record.exchange)
                })
            elif record_type.upper() == 'SOA':
                records.append({
                    "mname": str(record.mname),
                    "rname": str(record.rname),
                    "serial": record.serial,
                    "refresh": record.refresh,
                    "retry": record.retry,
                    "expire": record.expire,
                    "minimum": record.minimum
                })
            else:
                records.append(str(record))

        dns_result = {
            "domain": domain,
            "record_type": record_type.upper(),
            "records": records,
            "ttl": result.ttl,
            "timestamp": datetime.now().isoformat(),
            "success": True
        }

        print(f"✅ DNS Results for {domain} ({record_type.upper()}):")
        for i, record in enumerate(records, 1):
            if isinstance(record, dict):
                if record_type.upper() == 'MX':
                    print(f"   {i}. Priority: {record['priority']}, Exchange: {record['exchange']}")
                elif record_type.upper() == 'SOA':
                    print(f"   {i}. Primary NS: {record['mname']}, Admin: {record['rname']}")
            else:
                print(f"   {i}. {record}")
        print(f"   TTL: {result.ttl} seconds")

        logger.info(f"DNS lookup successful for {domain}")
        return dns_result

    except dns.resolver.NXDOMAIN:
        error_msg = f"Domain not found: {domain}"
        logger.warning(error_msg)
        print(f"⚠️  {error_msg}")
        return {"error": error_msg, "domain": domain, "success": False}

    except dns.resolver.NoAnswer:
        error_msg = f"No {record_type} records found for domain: {domain}"
        logger.warning(error_msg)
        print(f"⚠️  {error_msg}")
        return {"error": error_msg, "domain": domain, "success": False}

    except Exception as e:
        error_msg = f"DNS lookup failed for {domain}: {str(e)}"
        logger.error(error_msg)
        print(f"❌ {error_msg}")
        return {"error": error_msg, "domain": domain, "success": False}

def reverse_dns(ip: str) -> Dict[str, Any]:
    """Enhanced reverse DNS lookup with validation."""
    ip = sanitize_input(ip)

    if not validate_ip_address(ip):
        error_msg = f"Invalid IP address format: {ip}"
        logger.error(error_msg)
        print(f"❌ {error_msg}")
        return {"error": error_msg, "ip": ip}

    try:
        logger.info(f"Performing reverse DNS lookup for: {ip}")
        host_info = socket.gethostbyaddr(ip)

        result = {
            "ip": ip,
            "hostname": host_info[0],
            "aliases": host_info[1],
            "addresses": host_info[2],
            "timestamp": datetime.now().isoformat(),
            "success": True
        }

        print(f"✅ Reverse DNS Results for {ip}:")
        print(f"   Hostname: {result['hostname']}")
        if result['aliases']:
            print(f"   Aliases: {', '.join(result['aliases'])}")

        logger.info(f"Reverse DNS lookup successful for {ip}")
        return result

    except socket.herror as e:
        error_msg = f"Reverse DNS failed for {ip}: {str(e)}"
        logger.warning(error_msg)
        print(f"⚠️  {error_msg}")
        return {"error": error_msg, "ip": ip, "success": False}

    except Exception as e:
        error_msg = f"Unexpected error during reverse DNS for {ip}: {str(e)}"
        logger.error(error_msg)
        print(f"❌ {error_msg}")
        return {"error": error_msg, "ip": ip, "success": False}

@handle_api_error
def check_blacklist(ip: str) -> Dict[str, Any]:
    """Enhanced blacklist checking with multiple sources."""
    ip = sanitize_input(ip)

    if not validate_ip_address(ip):
        error_msg = f"Invalid IP address format: {ip}"
        logger.error(error_msg)
        print(f"❌ {error_msg}")
        return {"error": error_msg, "ip": ip}

    try:
        logger.info(f"Checking blacklist status for: {ip}")

        # Check multiple blacklist sources
        blacklist_sources = [
            f"https://www.abuseipdb.com/check/{ip}",
            f"https://www.virustotal.com/gui/ip-address/{ip}",
            f"https://talosintelligence.com/reputation_center/lookup?search={ip}"
        ]

        result = {
            "ip": ip,
            "blacklist_sources": blacklist_sources,
            "status": "Manual check required",
            "timestamp": datetime.now().isoformat(),
            "success": True
        }

        print(f"🔍 Blacklist Check for {ip}:")
        print(f"   Please manually check the following sources:")
        for i, source in enumerate(blacklist_sources, 1):
            print(f"   {i}. {source}")

        logger.info(f"Blacklist check initiated for {ip}")
        return result

    except Exception as e:
        error_msg = f"Blacklist check failed for {ip}: {str(e)}"
        logger.error(error_msg)
        print(f"❌ {error_msg}")
        return {"error": error_msg, "ip": ip, "success": False}

def hostname_lookup(ip: str) -> Dict[str, Any]:
    """Enhanced hostname lookup (alias for reverse DNS)."""
    return reverse_dns(ip)

def generate_random_ip(count: int = 1, ip_type: str = "public") -> Dict[str, Any]:
    """Enhanced random IP generator with options."""
    try:
        logger.info(f"Generating {count} random {ip_type} IP(s)")

        generated_ips = []

        for _ in range(count):
            if ip_type.lower() == "private":
                # Generate private IP ranges
                private_ranges = [
                    (10, 0, 0, 0, 255, 255, 255, 255),  # 10.0.0.0/8
                    (172, 16, 0, 0, 172, 31, 255, 255),  # **********/12
                    (192, 168, 0, 0, 192, 168, 255, 255)  # ***********/16
                ]
                range_choice = random.choice(private_ranges)
                if len(range_choice) == 8:
                    ip = f"{random.randint(range_choice[0], range_choice[4])}.{random.randint(range_choice[1], range_choice[5])}.{random.randint(range_choice[2], range_choice[6])}.{random.randint(range_choice[3], range_choice[7])}"
                else:
                    ip = f"{range_choice[0]}.{random.randint(range_choice[1], range_choice[5])}.{random.randint(range_choice[2], range_choice[6])}.{random.randint(range_choice[3], range_choice[7])}"
            else:
                # Generate public IP (avoid private ranges)
                while True:
                    ip = ".".join(str(random.randint(1, 254)) for _ in range(4))
                    ip_obj = ipaddress.ip_address(ip)
                    if ip_obj.is_global:
                        break

            generated_ips.append(ip)

        result = {
            "generated_ips": generated_ips,
            "count": count,
            "type": ip_type,
            "timestamp": datetime.now().isoformat(),
            "success": True
        }

        print(f"🎲 Generated {count} random {ip_type} IP(s):")
        for i, ip in enumerate(generated_ips, 1):
            print(f"   {i}. {ip}")

        logger.info(f"Successfully generated {count} random {ip_type} IP(s)")
        return result

    except Exception as e:
        error_msg = f"Random IP generation failed: {str(e)}"
        logger.error(error_msg)
        print(f"❌ {error_msg}")
        return {"error": error_msg, "success": False}

def run_api(host: str = "127.0.0.1", port: int = 5000, debug: bool = False):
    """Enhanced API server with comprehensive endpoints."""
    app = Flask(__name__)

    @app.route("/")
    def api_home():
        """API documentation endpoint."""
        endpoints = {
            "endpoints": {
                "/": "API documentation",
                "/ip-lookup": "IP address lookup (GET: ?ip=x.x.x.x)",
                "/whois": "WHOIS lookup (GET: ?ip=x.x.x.x)",
                "/dns": "DNS lookup (GET: ?domain=example.com&type=A)",
                "/reverse-dns": "Reverse DNS lookup (GET: ?ip=x.x.x.x)",
                "/blacklist": "Blacklist check (GET: ?ip=x.x.x.x)",
                "/random-ip": "Generate random IP (GET: ?count=1&type=public)",
                "/health": "Health check endpoint"
            },
            "version": "2.0",
            "timestamp": datetime.now().isoformat()
        }
        return jsonify(endpoints)

    @app.route("/health")
    def health_check():
        """Health check endpoint."""
        return jsonify({
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "2.0"
        })

    @app.route("/ip-lookup")
    def api_ip_lookup():
        """IP lookup API endpoint."""
        ip = request.args.get("ip")
        if not ip:
            return jsonify({"error": "Missing 'ip' parameter"}), 400

        result = ip_lookup(ip)
        return jsonify(result), 200 if result.get("success", False) else 400

    @app.route("/whois")
    def api_whois():
        """WHOIS lookup API endpoint."""
        ip = request.args.get("ip")
        if not ip:
            return jsonify({"error": "Missing 'ip' parameter"}), 400

        result = whois_lookup(ip)
        return jsonify(result), 200 if result.get("success", False) else 400

    @app.route("/dns")
    def api_dns():
        """DNS lookup API endpoint."""
        domain = request.args.get("domain")
        record_type = request.args.get("type", "A")

        if not domain:
            return jsonify({"error": "Missing 'domain' parameter"}), 400

        result = dns_lookup(domain, record_type)
        return jsonify(result), 200 if result.get("success", False) else 400

    @app.route("/reverse-dns")
    def api_reverse_dns():
        """Reverse DNS lookup API endpoint."""
        ip = request.args.get("ip")
        if not ip:
            return jsonify({"error": "Missing 'ip' parameter"}), 400

        result = reverse_dns(ip)
        return jsonify(result), 200 if result.get("success", False) else 400

    @app.route("/blacklist")
    def api_blacklist():
        """Blacklist check API endpoint."""
        ip = request.args.get("ip")
        if not ip:
            return jsonify({"error": "Missing 'ip' parameter"}), 400

        result = check_blacklist(ip)
        return jsonify(result), 200 if result.get("success", False) else 400

    @app.route("/random-ip")
    def api_random_ip():
        """Random IP generation API endpoint."""
        count = int(request.args.get("count", 1))
        ip_type = request.args.get("type", "public")

        if count > 100:
            return jsonify({"error": "Maximum count is 100"}), 400

        result = generate_random_ip(count, ip_type)
        return jsonify(result), 200 if result.get("success", False) else 400

    print(f"🚀 Starting IP Tools API server...")
    print(f"   Host: {host}")
    print(f"   Port: {port}")
    print(f"   Debug: {debug}")
    print(f"   API Documentation: http://{host}:{port}/")

    logger.info(f"Starting API server on {host}:{port}")
    app.run(host=host, port=port, debug=debug)
