version: '3.8'

services:
  ip-tools:
    build: .
    container_name: ip-tools-api
    ports:
      - "5000:5000"
    volumes:
      - ./results:/app/results
      - ./logs:/app/logs
      - ./ip_tools_config.json:/app/ip_tools_config.json
    environment:
      - API_HOST=0.0.0.0
      - API_PORT=5000
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  nginx:
    image: nginx:alpine
    container_name: ip-tools-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - ip-tools
    restart: unless-stopped

networks:
  default:
    name: ip-tools-network
