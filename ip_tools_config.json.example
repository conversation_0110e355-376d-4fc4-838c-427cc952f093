{"api": {"host": "127.0.0.1", "port": 5000, "debug": false, "rate_limit": {"enabled": true, "requests_per_minute": 60}}, "dns": {"default_servers": ["*******", "*******", "*******", "*******"], "timeout": 10, "doh_servers": {"cloudflare": "https://cloudflare-dns.com/dns-query", "google": "https://dns.google/dns-query", "quad9": "https://dns.quad9.net/dns-query"}, "default_doh_server": "cloudflare"}, "logging": {"level": "INFO", "file": "ip_tools.log", "max_size_mb": 10, "backup_count": 5, "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"}, "output": {"format": "json", "save_results": false, "results_directory": "results", "timestamp_format": "%Y%m%d_%H%M%S"}, "security": {"api_key_required": false, "allowed_ips": [], "max_batch_size": 100}, "features": {"geolocation": {"enabled": true, "apis": ["ipapi.co", "ip-api.com"]}, "reputation": {"enabled": true, "check_sources": ["abuseipdb", "virustotal"]}}, "cache": {"enabled": true, "ttl_seconds": 3600, "max_entries": 1000}}