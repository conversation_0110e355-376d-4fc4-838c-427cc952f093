# 🌐 IP Tools - Enhanced Network Analysis Toolkit v2.0

A comprehensive, professional-grade network analysis toolkit for IP address investigation, DNS analysis, WHOIS queries, and advanced network reconnaissance. Built with Python and designed for security professionals, network administrators, and developers.

## ✨ Features

### Core Functionality
- 🔍 **IP Address Lookup** - Comprehensive IP analysis with hostname resolution
- 📋 **WHOIS Lookup** - Detailed network registration information
- 🌐 **DNS Analysis** - Multi-record type DNS queries (A, AAAA, MX, CNAME, TXT, NS, SOA)
- 🔄 **Reverse DNS** - PTR record lookups and hostname resolution
- 🛡️ **Security Checks** - Blacklist checking and reputation analysis
- 🎲 **IP Generation** - Random IP generation (public/private)

### Advanced Features
- 🌍 **Geolocation** - IP-to-location mapping with ISP information
- 🔒 **DNS over HTTPS** - Secure DNS queries via DoH
- 🛡️ **DNS Security** - DNSSEC, SPF, DMARC, CAA record analysis
- 📊 **Comprehensive Analysis** - Multi-source IP intelligence gathering
- 🌐 **Subnet Analysis** - CIDR block analysis and network calculations
- 📋 **Batch Operations** - Process multiple IPs from files

### API & Integration
- 🚀 **REST API Server** - Full-featured API with comprehensive endpoints
- 📝 **JSON Output** - Structured data export
- ⚙️ **Configuration Management** - Flexible configuration system
- 📊 **Logging & Monitoring** - Comprehensive logging with rotation
- 🧪 **Testing Suite** - Complete unit and integration tests

## 🚀 Quick Start

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd ip_tools

# Install dependencies
pip install -r requirements.txt

# Run the application
python ip_tools.py
```

### Basic Usage

```bash
# Interactive mode
python ip_tools.py

# API server mode
python ip_tools.py
# Select option 8 for API server
```

## 📖 Detailed Usage

### Command Line Interface

The enhanced CLI provides an intuitive menu-driven interface:

```
🌐 IP Tools - Enhanced Network Analysis Toolkit v2.0
============================================================

📋 Available Functions:
   1. IP Address Lookup
   2. IP WHOIS Lookup
   3. DNS Lookup
   4. Reverse DNS Lookup
   5. IP Blacklist Check
   6. Hostname Lookup
   7. Random IP Generator
   8. Run API Server
   9. Batch Operations
   0. Exit
```

### API Server

Start the API server for programmatic access:

```bash
python ip_tools.py
# Select option 8
# Configure host (default: 127.0.0.1)
# Configure port (default: 5000)
# Enable/disable debug mode
```

#### API Endpoints

| Endpoint | Method | Parameters | Description |
|----------|--------|------------|-------------|
| `/` | GET | - | API documentation |
| `/health` | GET | - | Health check |
| `/ip-lookup` | GET | `ip` | IP address lookup |
| `/whois` | GET | `ip` | WHOIS information |
| `/dns` | GET | `domain`, `type` | DNS lookup |
| `/reverse-dns` | GET | `ip` | Reverse DNS lookup |
| `/blacklist` | GET | `ip` | Blacklist check |
| `/random-ip` | GET | `count`, `type` | Generate random IPs |

#### API Examples

```bash
# IP lookup
curl "http://localhost:5000/ip-lookup?ip=*******"

# DNS lookup
curl "http://localhost:5000/dns?domain=example.com&type=A"

# WHOIS lookup
curl "http://localhost:5000/whois?ip=*******"

# Generate random IPs
curl "http://localhost:5000/random-ip?count=5&type=public"
```

## ⚙️ Configuration

The toolkit uses a JSON configuration file (`ip_tools_config.json`) for customization:

```json
{
  "api": {
    "host": "127.0.0.1",
    "port": 5000,
    "debug": false
  },
  "dns": {
    "default_servers": ["*******", "*******"],
    "timeout": 10
  },
  "logging": {
    "level": "INFO",
    "file": "ip_tools.log"
  },
  "features": {
    "geolocation": {
      "enabled": true
    },
    "reputation": {
      "enabled": true
    }
  }
}
```

## 🧪 Testing

Run the comprehensive test suite:

```bash
# Install test dependencies
pip install pytest pytest-cov

# Run all tests
python -m pytest test_ip_tools.py -v

# Run with coverage
python -m pytest test_ip_tools.py --cov=. --cov-report=html
```

## 📦 Dependencies

Core dependencies:
- `requests>=2.28.0` - HTTP requests
- `dnspython>=2.2.0` - DNS operations
- `ipwhois>=1.2.0` - WHOIS lookups
- `Flask>=2.2.0` - API server

Enhanced features:
- `colorama>=0.4.6` - Colored output
- `rich>=13.0.0` - Rich text formatting
- `click>=8.1.0` - CLI framework
- `python-dotenv>=0.19.0` - Environment variables

Development:
- `pytest>=7.0.0` - Testing framework
- `pytest-cov>=4.0.0` - Coverage reporting

## 🔧 Advanced Usage

### Batch Operations

Process multiple IP addresses from a file:

1. Create a text file with one IP per line:
```
*******
*******
**************
```

2. Select option 9 (Batch Operations) from the main menu
3. Enter the filename when prompted
4. Results will be saved to a JSON file

### DNS over HTTPS (DoH)

Use secure DNS queries:

```python
from dns_tools import dns_over_https

# Query using Cloudflare DoH
result = dns_over_https("example.com", "A", "cloudflare")

# Query using Google DoH
result = dns_over_https("example.com", "MX", "google")
```

### Comprehensive IP Analysis

Get detailed information about an IP:

```python
from advanced_tools import analyze_ip_comprehensive

result = analyze_ip_comprehensive("*******")
# Returns: basic lookup, WHOIS, reverse DNS, geolocation, reputation
```

### Subnet Analysis

Analyze network subnets:

```python
from advanced_tools import subnet_analyzer

result = subnet_analyzer("***********/24")
# Returns: network info, broadcast, netmask, host count, etc.
```

## 🐳 Docker Support

### Dockerfile

```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 5000

CMD ["python", "ip_tools.py"]
```

### Docker Compose

```yaml
version: '3.8'
services:
  ip-tools:
    build: .
    ports:
      - "5000:5000"
    volumes:
      - ./results:/app/results
      - ./logs:/app/logs
    environment:
      - API_HOST=0.0.0.0
      - API_PORT=5000
```

### Build and Run

```bash
# Build image
docker build -t ip-tools .

# Run container
docker run -p 5000:5000 ip-tools

# Run with docker-compose
docker-compose up -d
```

## 🔒 Security Considerations

### API Security

- **Rate Limiting**: Configure request limits in config file
- **IP Filtering**: Restrict API access to specific IPs
- **Authentication**: Enable API key authentication for production

### Data Privacy

- **Logging**: Review log levels to avoid sensitive data exposure
- **Results Storage**: Secure storage of analysis results
- **Network Access**: Monitor outbound connections for data leaks

## 🚀 Deployment

### Production Deployment

1. **Environment Setup**:
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt
```

2. **Configuration**:
```bash
# Copy and modify config
cp ip_tools_config.json.example ip_tools_config.json
# Edit configuration as needed
```

3. **Service Setup** (Linux):
```bash
# Create systemd service
sudo nano /etc/systemd/system/ip-tools.service

[Unit]
Description=IP Tools API Server
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/ip_tools
ExecStart=/path/to/venv/bin/python ip_tools.py
Restart=always

[Install]
WantedBy=multi-user.target

# Enable and start service
sudo systemctl enable ip-tools
sudo systemctl start ip-tools
```

### Nginx Reverse Proxy

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

## 📊 Monitoring & Logging

### Log Files

- `ip_tools.log` - Application logs
- `results/` - Analysis results (if enabled)
- `access.log` - API access logs (if configured)

### Health Monitoring

```bash
# Check API health
curl http://localhost:5000/health

# Monitor logs
tail -f ip_tools.log

# Check system resources
htop
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Setup

```bash
# Clone repository
git clone <repository-url>
cd ip_tools

# Install development dependencies
pip install -r requirements.txt
pip install -e .

# Run tests
python -m pytest

# Run with coverage
python -m pytest --cov=. --cov-report=html
```

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- DNS resolution powered by `dnspython`
- WHOIS data from `ipwhois` library
- Geolocation services from multiple providers
- Security checks via various threat intelligence sources

## 📞 Support

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/username/ip_tools/issues)
- 📖 Documentation: [Wiki](https://github.com/username/ip_tools/wiki)
- 💬 Discussions: [GitHub Discussions](https://github.com/username/ip_tools/discussions)

---

**Made with ❤️ for the cybersecurity and network administration community**
