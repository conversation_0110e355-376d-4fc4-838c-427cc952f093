@echo off
REM IP Tools Installation Script for Windows
REM Version 2.0

echo 🌐 IP Tools - Enhanced Network Analysis Toolkit v2.0
echo ==================================================
echo Starting installation...

REM Check Python version
echo 📋 Checking Python version...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.7+ from https://python.org
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set python_version=%%i
echo ✅ Python %python_version% found

REM Check pip
echo 📦 Checking pip...
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip is not available
    echo Please ensure pip is installed with Python
    pause
    exit /b 1
)
echo ✅ pip is available

REM Create virtual environment
set /p create_venv="🔧 Create virtual environment? (recommended) [Y/n]: "
if /i "%create_venv%"=="" set create_venv=Y
if /i "%create_venv%"=="Y" (
    echo 📁 Creating virtual environment...
    python -m venv venv
    call venv\Scripts\activate.bat
    echo ✅ Virtual environment created and activated
)

REM Install dependencies
echo 📦 Installing dependencies...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)
echo ✅ Dependencies installed

REM Create configuration file
echo ⚙️ Setting up configuration...
if not exist "ip_tools_config.json" (
    copy "ip_tools_config.json.example" "ip_tools_config.json" >nul
    echo ✅ Configuration file created from example
) else (
    echo ℹ️  Configuration file already exists
)

REM Create directories
echo 📁 Creating directories...
if not exist "results" mkdir results
if not exist "logs" mkdir logs
echo ✅ Directories created

REM Run tests (optional)
set /p run_tests="🧪 Run tests to verify installation? [Y/n]: "
if /i "%run_tests%"=="" set run_tests=Y
if /i "%run_tests%"=="Y" (
    echo 🧪 Running tests...
    python -m pytest test_ip_tools.py -v 2>nul
    if errorlevel 1 (
        echo ⚠️  Some tests failed or pytest not available
    ) else (
        echo ✅ All tests passed
    )
)

echo.
echo 🎉 Installation completed successfully!
echo.
echo 📋 Next steps:
echo 1. Review configuration: notepad ip_tools_config.json
echo 2. Run the application: python ip_tools.py
echo 3. Start API server: python ip_tools.py (option 8)
echo.
echo 📖 Documentation: README.md
echo 🐛 Issues: https://github.com/username/ip_tools/issues
echo.

if /i "%create_venv%"=="Y" (
    echo 💡 Remember to activate the virtual environment:
    echo    venv\Scripts\activate.bat
    echo.
)

echo 🚀 Ready to analyze networks!
pause
