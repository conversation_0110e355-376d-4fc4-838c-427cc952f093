#!/usr/bin/env python3
"""
Enhanced DNS Tools - Advanced DNS Analysis Features
Version 2.0

Advanced DNS functionality including DNS over HTTPS, security checks,
and comprehensive DNS record analysis.
"""

import requests
import json
import base64
from typing import Dict, List, Optional, Union, Any
from datetime import datetime
import logging
import dns.resolver
import dns.query
import dns.message

logger = logging.getLogger(__name__)

def dns_over_https(domain: str, record_type: str = 'A', doh_server: str = 'cloudflare') -> Dict[str, Any]:
    """
    Perform DNS lookup using DNS over HTTPS (DoH).
    
    Args:
        domain: Domain to lookup
        record_type: DNS record type
        doh_server: DoH server to use ('cloudflare', 'google', 'quad9')
    
    Returns:
        Dictionary containing DNS results
    """
    from utils import validate_domain, sanitize_input
    
    domain = sanitize_input(domain)
    if not validate_domain(domain):
        return {"error": f"Invalid domain: {domain}", "domain": domain}
    
    doh_servers = {
        'cloudflare': 'https://cloudflare-dns.com/dns-query',
        'google': 'https://dns.google/dns-query',
        'quad9': 'https://dns.quad9.net/dns-query'
    }
    
    if doh_server not in doh_servers:
        return {"error": f"Invalid DoH server: {doh_server}", "domain": domain}
    
    try:
        logger.info(f"Performing DoH lookup for {domain} ({record_type}) via {doh_server}")
        
        # Map record types to numbers
        record_types = {
            'A': 1, 'NS': 2, 'CNAME': 5, 'SOA': 6, 'PTR': 12,
            'MX': 15, 'TXT': 16, 'AAAA': 28, 'SRV': 33
        }
        
        type_num = record_types.get(record_type.upper(), 1)
        
        params = {
            'name': domain,
            'type': type_num,
            'do': 'false',
            'cd': 'false'
        }
        
        headers = {
            'Accept': 'application/dns-json'
        }
        
        response = requests.get(doh_servers[doh_server], params=params, headers=headers, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        
        result = {
            "domain": domain,
            "record_type": record_type.upper(),
            "doh_server": doh_server,
            "status": data.get("Status", -1),
            "answers": [],
            "timestamp": datetime.now().isoformat(),
            "success": True
        }
        
        if "Answer" in data:
            for answer in data["Answer"]:
                result["answers"].append({
                    "name": answer.get("name"),
                    "type": answer.get("type"),
                    "ttl": answer.get("TTL"),
                    "data": answer.get("data")
                })
        
        print(f"🔒 DNS over HTTPS Results for {domain} ({record_type}):")
        print(f"   Server: {doh_server}")
        print(f"   Status: {result['status']} ({'Success' if result['status'] == 0 else 'Error'})")
        
        if result["answers"]:
            for i, answer in enumerate(result["answers"], 1):
                print(f"   {i}. {answer['data']} (TTL: {answer['ttl']})")
        else:
            print("   No records found")
        
        return result
        
    except Exception as e:
        error_msg = f"DoH lookup failed for {domain}: {str(e)}"
        logger.error(error_msg)
        print(f"❌ {error_msg}")
        return {"error": error_msg, "domain": domain, "success": False}

def dns_security_check(domain: str) -> Dict[str, Any]:
    """
    Perform DNS security checks including DNSSEC validation.
    
    Args:
        domain: Domain to check
    
    Returns:
        Dictionary containing security check results
    """
    from utils import validate_domain, sanitize_input
    
    domain = sanitize_input(domain)
    if not validate_domain(domain):
        return {"error": f"Invalid domain: {domain}", "domain": domain}
    
    try:
        logger.info(f"Performing DNS security check for: {domain}")
        
        security_results = {
            "domain": domain,
            "checks": {},
            "timestamp": datetime.now().isoformat(),
            "success": True
        }
        
        # Check DNSSEC
        try:
            resolver = dns.resolver.Resolver()
            resolver.use_edns(0, dns.flags.DO, 4096)
            
            # Try to get DNSKEY record
            try:
                dnskey_response = resolver.resolve(domain, 'DNSKEY')
                security_results["checks"]["dnssec"] = {
                    "enabled": True,
                    "dnskey_records": len(dnskey_response),
                    "details": "DNSSEC appears to be enabled"
                }
            except:
                security_results["checks"]["dnssec"] = {
                    "enabled": False,
                    "details": "No DNSKEY records found"
                }
        except Exception as e:
            security_results["checks"]["dnssec"] = {
                "enabled": "unknown",
                "error": str(e)
            }
        
        # Check for common DNS security records
        security_records = ['SPF', 'DMARC', 'DKIM']
        
        for record_type in security_records:
            try:
                if record_type == 'SPF':
                    # SPF is in TXT records
                    txt_records = dns.resolver.resolve(domain, 'TXT')
                    spf_found = False
                    for record in txt_records:
                        if 'v=spf1' in str(record).lower():
                            spf_found = True
                            break
                    security_results["checks"]["spf"] = {
                        "present": spf_found,
                        "details": "SPF record found" if spf_found else "No SPF record"
                    }
                
                elif record_type == 'DMARC':
                    # DMARC is in TXT records at _dmarc subdomain
                    dmarc_domain = f"_dmarc.{domain}"
                    try:
                        dmarc_records = dns.resolver.resolve(dmarc_domain, 'TXT')
                        dmarc_found = any('v=DMARC1' in str(record) for record in dmarc_records)
                        security_results["checks"]["dmarc"] = {
                            "present": dmarc_found,
                            "details": "DMARC record found" if dmarc_found else "No DMARC record"
                        }
                    except:
                        security_results["checks"]["dmarc"] = {
                            "present": False,
                            "details": "No DMARC record"
                        }
                
                elif record_type == 'DKIM':
                    # DKIM check is more complex, just note it should be checked
                    security_results["checks"]["dkim"] = {
                        "present": "unknown",
                        "details": "DKIM requires specific selector knowledge"
                    }
                    
            except Exception as e:
                security_results["checks"][record_type.lower()] = {
                    "present": "error",
                    "error": str(e)
                }
        
        # Check for CAA records
        try:
            caa_records = dns.resolver.resolve(domain, 'CAA')
            security_results["checks"]["caa"] = {
                "present": True,
                "count": len(caa_records),
                "details": "CAA records found"
            }
        except:
            security_results["checks"]["caa"] = {
                "present": False,
                "details": "No CAA records"
            }
        
        print(f"🛡️  DNS Security Check for {domain}:")
        
        dnssec = security_results["checks"].get("dnssec", {})
        print(f"   DNSSEC: {'✅ Enabled' if dnssec.get('enabled') else '❌ Disabled'}")
        
        spf = security_results["checks"].get("spf", {})
        print(f"   SPF: {'✅ Present' if spf.get('present') else '❌ Missing'}")
        
        dmarc = security_results["checks"].get("dmarc", {})
        print(f"   DMARC: {'✅ Present' if dmarc.get('present') else '❌ Missing'}")
        
        caa = security_results["checks"].get("caa", {})
        print(f"   CAA: {'✅ Present' if caa.get('present') else '❌ Missing'}")
        
        return security_results
        
    except Exception as e:
        error_msg = f"DNS security check failed for {domain}: {str(e)}"
        logger.error(error_msg)
        print(f"❌ {error_msg}")
        return {"error": error_msg, "domain": domain, "success": False}

def dns_trace(domain: str) -> Dict[str, Any]:
    """
    Trace DNS resolution path.
    
    Args:
        domain: Domain to trace
    
    Returns:
        Dictionary containing DNS trace results
    """
    from utils import validate_domain, sanitize_input
    
    domain = sanitize_input(domain)
    if not validate_domain(domain):
        return {"error": f"Invalid domain: {domain}", "domain": domain}
    
    try:
        logger.info(f"Tracing DNS resolution for: {domain}")
        
        trace_results = {
            "domain": domain,
            "trace_path": [],
            "timestamp": datetime.now().isoformat(),
            "success": True
        }
        
        # Get root servers
        root_servers = [
            "198.41.0.4",  # a.root-servers.net
            "199.9.14.201",  # b.root-servers.net
            "192.33.4.12",  # c.root-servers.net
        ]
        
        current_domain = domain
        query_servers = root_servers[:1]  # Start with one root server
        
        print(f"🔍 DNS Trace for {domain}:")
        
        step = 1
        while current_domain and step <= 10:  # Limit steps to prevent infinite loops
            try:
                print(f"   Step {step}: Querying {query_servers[0]} for {current_domain}")
                
                # This is a simplified trace - full implementation would be more complex
                resolver = dns.resolver.Resolver()
                resolver.nameservers = query_servers
                
                try:
                    answer = resolver.resolve(current_domain, 'A')
                    trace_results["trace_path"].append({
                        "step": step,
                        "server": query_servers[0],
                        "query": current_domain,
                        "result": "Answer found",
                        "records": [str(record) for record in answer]
                    })
                    print(f"   ✅ Answer: {[str(record) for record in answer]}")
                    break
                    
                except dns.resolver.NXDOMAIN:
                    trace_results["trace_path"].append({
                        "step": step,
                        "server": query_servers[0],
                        "query": current_domain,
                        "result": "NXDOMAIN"
                    })
                    print(f"   ❌ NXDOMAIN")
                    break
                    
                except Exception as e:
                    trace_results["trace_path"].append({
                        "step": step,
                        "server": query_servers[0],
                        "query": current_domain,
                        "result": f"Error: {str(e)}"
                    })
                    break
                    
            except Exception as e:
                logger.warning(f"DNS trace step {step} failed: {e}")
                break
            
            step += 1
        
        return trace_results
        
    except Exception as e:
        error_msg = f"DNS trace failed for {domain}: {str(e)}"
        logger.error(error_msg)
        print(f"❌ {error_msg}")
        return {"error": error_msg, "domain": domain, "success": False}

def bulk_dns_lookup(domains: List[str], record_type: str = 'A') -> Dict[str, Any]:
    """
    Perform bulk DNS lookups for multiple domains.
    
    Args:
        domains: List of domains to lookup
        record_type: DNS record type
    
    Returns:
        Dictionary containing bulk lookup results
    """
    from utils import dns_lookup
    
    try:
        logger.info(f"Performing bulk DNS lookup for {len(domains)} domains")
        
        results = {
            "domains": domains,
            "record_type": record_type,
            "results": [],
            "summary": {
                "total": len(domains),
                "successful": 0,
                "failed": 0
            },
            "timestamp": datetime.now().isoformat(),
            "success": True
        }
        
        print(f"📋 Bulk DNS Lookup for {len(domains)} domains ({record_type}):")
        
        for i, domain in enumerate(domains, 1):
            print(f"   {i}/{len(domains)}: {domain}")
            
            result = dns_lookup(domain, record_type)
            results["results"].append(result)
            
            if result.get("success"):
                results["summary"]["successful"] += 1
            else:
                results["summary"]["failed"] += 1
        
        print(f"\n📊 Bulk Lookup Summary:")
        print(f"   Total: {results['summary']['total']}")
        print(f"   Successful: {results['summary']['successful']}")
        print(f"   Failed: {results['summary']['failed']}")
        
        return results
        
    except Exception as e:
        error_msg = f"Bulk DNS lookup failed: {str(e)}"
        logger.error(error_msg)
        print(f"❌ {error_msg}")
        return {"error": error_msg, "success": False}
