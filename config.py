#!/usr/bin/env python3
"""
Configuration Management for IP Tools
Version 2.0

Handles configuration loading, validation, and management for the IP Tools suite.
"""

import os
import json
import logging
from typing import Dict, Any, Optional
from pathlib import Path

class ConfigManager:
    """Configuration manager for IP Tools."""
    
    def __init__(self, config_file: str = "ip_tools_config.json"):
        self.config_file = Path(config_file)
        self.config = self._load_default_config()
        self.load_config()
    
    def _load_default_config(self) -> Dict[str, Any]:
        """Load default configuration."""
        return {
            "api": {
                "host": "127.0.0.1",
                "port": 5000,
                "debug": False,
                "rate_limit": {
                    "enabled": True,
                    "requests_per_minute": 60
                }
            },
            "dns": {
                "default_servers": [
                    "*******",
                    "*******",
                    "*******",
                    "*******"
                ],
                "timeout": 10,
                "doh_servers": {
                    "cloudflare": "https://cloudflare-dns.com/dns-query",
                    "google": "https://dns.google/dns-query",
                    "quad9": "https://dns.quad9.net/dns-query"
                },
                "default_doh_server": "cloudflare"
            },
            "logging": {
                "level": "INFO",
                "file": "ip_tools.log",
                "max_size_mb": 10,
                "backup_count": 5,
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            },
            "output": {
                "format": "json",
                "save_results": False,
                "results_directory": "results",
                "timestamp_format": "%Y%m%d_%H%M%S"
            },
            "security": {
                "api_key_required": False,
                "allowed_ips": [],
                "max_batch_size": 100
            },
            "features": {
                "geolocation": {
                    "enabled": True,
                    "apis": [
                        "ipapi.co",
                        "ip-api.com"
                    ]
                },
                "reputation": {
                    "enabled": True,
                    "check_sources": [
                        "abuseipdb",
                        "virustotal"
                    ]
                }
            },
            "cache": {
                "enabled": True,
                "ttl_seconds": 3600,
                "max_entries": 1000
            }
        }
    
    def load_config(self) -> bool:
        """Load configuration from file."""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r') as f:
                    file_config = json.load(f)
                
                # Merge with defaults
                self._merge_config(self.config, file_config)
                logging.info(f"Configuration loaded from {self.config_file}")
                return True
            else:
                logging.info("No config file found, using defaults")
                self.save_config()  # Create default config file
                return False
                
        except Exception as e:
            logging.error(f"Error loading config: {e}")
            return False
    
    def save_config(self) -> bool:
        """Save current configuration to file."""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
            logging.info(f"Configuration saved to {self.config_file}")
            return True
        except Exception as e:
            logging.error(f"Error saving config: {e}")
            return False
    
    def _merge_config(self, default: Dict[str, Any], override: Dict[str, Any]) -> None:
        """Recursively merge configuration dictionaries."""
        for key, value in override.items():
            if key in default and isinstance(default[key], dict) and isinstance(value, dict):
                self._merge_config(default[key], value)
            else:
                default[key] = value
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """
        Get configuration value using dot notation.
        
        Args:
            key_path: Dot-separated path (e.g., "api.host")
            default: Default value if key not found
        
        Returns:
            Configuration value or default
        """
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any) -> None:
        """
        Set configuration value using dot notation.
        
        Args:
            key_path: Dot-separated path (e.g., "api.host")
            value: Value to set
        """
        keys = key_path.split('.')
        config = self.config
        
        # Navigate to parent
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        # Set value
        config[keys[-1]] = value
    
    def validate_config(self) -> Dict[str, Any]:
        """
        Validate configuration and return validation results.
        
        Returns:
            Dictionary with validation results
        """
        validation_results = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        # Validate API configuration
        api_host = self.get("api.host")
        if not isinstance(api_host, str) or not api_host:
            validation_results["errors"].append("api.host must be a non-empty string")
            validation_results["valid"] = False
        
        api_port = self.get("api.port")
        if not isinstance(api_port, int) or not (1 <= api_port <= 65535):
            validation_results["errors"].append("api.port must be an integer between 1 and 65535")
            validation_results["valid"] = False
        
        # Validate logging configuration
        log_level = self.get("logging.level")
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if log_level not in valid_levels:
            validation_results["errors"].append(f"logging.level must be one of {valid_levels}")
            validation_results["valid"] = False
        
        # Validate output directory
        results_dir = self.get("output.results_directory")
        if results_dir:
            try:
                Path(results_dir).mkdir(parents=True, exist_ok=True)
            except Exception as e:
                validation_results["warnings"].append(f"Cannot create results directory: {e}")
        
        # Validate DNS servers
        dns_servers = self.get("dns.default_servers", [])
        if not isinstance(dns_servers, list) or not dns_servers:
            validation_results["warnings"].append("dns.default_servers should be a non-empty list")
        
        return validation_results
    
    def get_api_config(self) -> Dict[str, Any]:
        """Get API-specific configuration."""
        return self.get("api", {})
    
    def get_dns_config(self) -> Dict[str, Any]:
        """Get DNS-specific configuration."""
        return self.get("dns", {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """Get logging-specific configuration."""
        return self.get("logging", {})
    
    def get_output_config(self) -> Dict[str, Any]:
        """Get output-specific configuration."""
        return self.get("output", {})
    
    def is_feature_enabled(self, feature: str) -> bool:
        """Check if a feature is enabled."""
        return self.get(f"features.{feature}.enabled", False)
    
    def get_cache_config(self) -> Dict[str, Any]:
        """Get cache-specific configuration."""
        return self.get("cache", {})

# Global configuration instance
config = ConfigManager()

def setup_logging():
    """Setup logging based on configuration."""
    log_config = config.get_logging_config()
    
    # Create results directory if it doesn't exist
    results_dir = config.get("output.results_directory", "results")
    Path(results_dir).mkdir(parents=True, exist_ok=True)
    
    # Setup logging
    log_level = getattr(logging, log_config.get("level", "INFO"))
    log_format = log_config.get("format", "%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    log_file = log_config.get("file", "ip_tools.log")
    
    # Configure root logger
    logging.basicConfig(
        level=log_level,
        format=log_format,
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    # Set up rotating file handler if max_size_mb is specified
    max_size_mb = log_config.get("max_size_mb")
    if max_size_mb:
        from logging.handlers import RotatingFileHandler
        
        # Remove existing file handler
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            if isinstance(handler, logging.FileHandler):
                root_logger.removeHandler(handler)
        
        # Add rotating file handler
        rotating_handler = RotatingFileHandler(
            log_file,
            maxBytes=max_size_mb * 1024 * 1024,
            backupCount=log_config.get("backup_count", 5)
        )
        rotating_handler.setFormatter(logging.Formatter(log_format))
        root_logger.addHandler(rotating_handler)

def get_config() -> ConfigManager:
    """Get the global configuration instance."""
    return config

if __name__ == "__main__":
    # Test configuration
    setup_logging()
    
    print("🔧 IP Tools Configuration Test")
    print(f"Config file: {config.config_file}")
    print(f"API host: {config.get('api.host')}")
    print(f"API port: {config.get('api.port')}")
    
    validation = config.validate_config()
    print(f"Configuration valid: {validation['valid']}")
    
    if validation['errors']:
        print("Errors:")
        for error in validation['errors']:
            print(f"  - {error}")
    
    if validation['warnings']:
        print("Warnings:")
        for warning in validation['warnings']:
            print(f"  - {warning}")
