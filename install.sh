#!/bin/bash

# IP Tools Installation Script
# Version 2.0

set -e

echo "🌐 IP Tools - Enhanced Network Analysis Toolkit v2.0"
echo "=================================================="
echo "Starting installation..."

# Check Python version
echo "📋 Checking Python version..."
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
required_version="3.7"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" = "$required_version" ]; then
    echo "✅ Python $python_version is compatible"
else
    echo "❌ Python $required_version or higher is required. Found: $python_version"
    exit 1
fi

# Check if pip is available
echo "📦 Checking pip..."
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 is not installed. Please install pip3 first."
    exit 1
fi
echo "✅ pip3 is available"

# Create virtual environment (optional)
read -p "🔧 Create virtual environment? (recommended) [Y/n]: " create_venv
create_venv=${create_venv:-Y}

if [[ $create_venv =~ ^[Yy]$ ]]; then
    echo "📁 Creating virtual environment..."
    python3 -m venv venv
    source venv/bin/activate
    echo "✅ Virtual environment created and activated"
fi

# Install dependencies
echo "📦 Installing dependencies..."
pip3 install -r requirements.txt

# Create configuration file
echo "⚙️ Setting up configuration..."
if [ ! -f "ip_tools_config.json" ]; then
    cp ip_tools_config.json.example ip_tools_config.json
    echo "✅ Configuration file created from example"
else
    echo "ℹ️  Configuration file already exists"
fi

# Create directories
echo "📁 Creating directories..."
mkdir -p results logs
echo "✅ Directories created"

# Set permissions
echo "🔒 Setting permissions..."
chmod +x ip_tools.py
chmod +x install.sh

# Run tests (optional)
read -p "🧪 Run tests to verify installation? [Y/n]: " run_tests
run_tests=${run_tests:-Y}

if [[ $run_tests =~ ^[Yy]$ ]]; then
    echo "🧪 Running tests..."
    if command -v pytest &> /dev/null; then
        python3 -m pytest test_ip_tools.py -v
        echo "✅ All tests passed"
    else
        echo "⚠️  pytest not available, skipping tests"
    fi
fi

echo ""
echo "🎉 Installation completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Review configuration: nano ip_tools_config.json"
echo "2. Run the application: python3 ip_tools.py"
echo "3. Start API server: python3 ip_tools.py (option 8)"
echo ""
echo "📖 Documentation: README.md"
echo "🐛 Issues: https://github.com/username/ip_tools/issues"
echo ""

if [[ $create_venv =~ ^[Yy]$ ]]; then
    echo "💡 Remember to activate the virtual environment:"
    echo "   source venv/bin/activate"
    echo ""
fi

echo "🚀 Ready to analyze networks!"
