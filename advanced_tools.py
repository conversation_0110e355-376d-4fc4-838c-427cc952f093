#!/usr/bin/env python3
"""
Advanced IP Tools - Extended Network Analysis Features
Version 2.0

Advanced features including geolocation, ISP information, threat intelligence,
and enhanced network analysis capabilities.
"""

import requests
import json
import time
from typing import Dict, List, Optional, Union, Any
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

def get_ip_geolocation(ip: str, api_key: Optional[str] = None) -> Dict[str, Any]:
    """
    Get geolocation information for an IP address using multiple free APIs.
    
    Args:
        ip: IP address to lookup
        api_key: Optional API key for premium services
    
    Returns:
        Dictionary containing geolocation data
    """
    from utils import validate_ip_address, sanitize_input
    
    ip = sanitize_input(ip)
    if not validate_ip_address(ip):
        return {"error": f"Invalid IP address: {ip}", "ip": ip}
    
    try:
        logger.info(f"Getting geolocation for IP: {ip}")
        
        # Try multiple free geolocation APIs
        apis = [
            {
                "name": "ipapi.co",
                "url": f"https://ipapi.co/{ip}/json/",
                "parser": lambda r: {
                    "ip": r.get("ip"),
                    "city": r.get("city"),
                    "region": r.get("region"),
                    "country": r.get("country_name"),
                    "country_code": r.get("country_code"),
                    "latitude": r.get("latitude"),
                    "longitude": r.get("longitude"),
                    "timezone": r.get("timezone"),
                    "isp": r.get("org"),
                    "asn": r.get("asn")
                }
            },
            {
                "name": "ip-api.com",
                "url": f"http://ip-api.com/json/{ip}",
                "parser": lambda r: {
                    "ip": r.get("query"),
                    "city": r.get("city"),
                    "region": r.get("regionName"),
                    "country": r.get("country"),
                    "country_code": r.get("countryCode"),
                    "latitude": r.get("lat"),
                    "longitude": r.get("lon"),
                    "timezone": r.get("timezone"),
                    "isp": r.get("isp"),
                    "asn": r.get("as")
                }
            }
        ]
        
        for api in apis:
            try:
                response = requests.get(api["url"], timeout=10)
                response.raise_for_status()
                data = response.json()
                
                if data and not data.get("error"):
                    parsed_data = api["parser"](data)
                    result = {
                        "ip": ip,
                        "geolocation": parsed_data,
                        "source": api["name"],
                        "timestamp": datetime.now().isoformat(),
                        "success": True
                    }
                    
                    print(f"🌍 Geolocation Results for {ip}:")
                    print(f"   Location: {parsed_data.get('city', 'Unknown')}, {parsed_data.get('region', 'Unknown')}, {parsed_data.get('country', 'Unknown')}")
                    print(f"   Coordinates: {parsed_data.get('latitude', 'Unknown')}, {parsed_data.get('longitude', 'Unknown')}")
                    print(f"   ISP: {parsed_data.get('isp', 'Unknown')}")
                    print(f"   ASN: {parsed_data.get('asn', 'Unknown')}")
                    print(f"   Timezone: {parsed_data.get('timezone', 'Unknown')}")
                    print(f"   Source: {api['name']}")
                    
                    return result
                    
            except Exception as e:
                logger.warning(f"Failed to get geolocation from {api['name']}: {e}")
                continue
        
        error_msg = f"All geolocation APIs failed for IP: {ip}"
        logger.error(error_msg)
        print(f"❌ {error_msg}")
        return {"error": error_msg, "ip": ip, "success": False}
        
    except Exception as e:
        error_msg = f"Geolocation lookup failed for {ip}: {str(e)}"
        logger.error(error_msg)
        print(f"❌ {error_msg}")
        return {"error": error_msg, "ip": ip, "success": False}

def get_ip_reputation(ip: str) -> Dict[str, Any]:
    """
    Check IP reputation using multiple threat intelligence sources.
    
    Args:
        ip: IP address to check
    
    Returns:
        Dictionary containing reputation data
    """
    from utils import validate_ip_address, sanitize_input
    
    ip = sanitize_input(ip)
    if not validate_ip_address(ip):
        return {"error": f"Invalid IP address: {ip}", "ip": ip}
    
    try:
        logger.info(f"Checking IP reputation for: {ip}")
        
        # Free reputation check APIs
        reputation_sources = []
        
        # Check with AbuseIPDB (requires API key for full functionality)
        try:
            # This is a basic check - for full functionality, users need API key
            abuse_url = f"https://www.abuseipdb.com/check/{ip}"
            reputation_sources.append({
                "source": "AbuseIPDB",
                "url": abuse_url,
                "status": "Manual check required"
            })
        except Exception as e:
            logger.warning(f"AbuseIPDB check failed: {e}")
        
        # Check with VirusTotal (basic info without API key)
        try:
            vt_url = f"https://www.virustotal.com/gui/ip-address/{ip}"
            reputation_sources.append({
                "source": "VirusTotal",
                "url": vt_url,
                "status": "Manual check required"
            })
        except Exception as e:
            logger.warning(f"VirusTotal check failed: {e}")
        
        result = {
            "ip": ip,
            "reputation_sources": reputation_sources,
            "recommendation": "Manual verification recommended",
            "timestamp": datetime.now().isoformat(),
            "success": True
        }
        
        print(f"🛡️  IP Reputation Check for {ip}:")
        print(f"   Status: Manual verification required")
        print(f"   Check these sources:")
        for source in reputation_sources:
            print(f"   - {source['source']}: {source['url']}")
        
        return result
        
    except Exception as e:
        error_msg = f"IP reputation check failed for {ip}: {str(e)}"
        logger.error(error_msg)
        print(f"❌ {error_msg}")
        return {"error": error_msg, "ip": ip, "success": False}

def analyze_ip_comprehensive(ip: str) -> Dict[str, Any]:
    """
    Perform comprehensive IP analysis combining multiple data sources.
    
    Args:
        ip: IP address to analyze
    
    Returns:
        Dictionary containing comprehensive analysis results
    """
    from utils import ip_lookup, whois_lookup, reverse_dns, validate_ip_address, sanitize_input
    
    ip = sanitize_input(ip)
    if not validate_ip_address(ip):
        return {"error": f"Invalid IP address: {ip}", "ip": ip}
    
    try:
        logger.info(f"Performing comprehensive analysis for IP: {ip}")
        
        print(f"🔍 Comprehensive IP Analysis for {ip}")
        print("=" * 50)
        
        # Collect all data
        analysis_results = {
            "ip": ip,
            "timestamp": datetime.now().isoformat(),
            "analysis": {}
        }
        
        # Basic IP lookup
        print("1. Basic IP Lookup...")
        basic_lookup = ip_lookup(ip)
        analysis_results["analysis"]["basic_lookup"] = basic_lookup
        
        # WHOIS lookup
        print("\n2. WHOIS Information...")
        whois_data = whois_lookup(ip)
        analysis_results["analysis"]["whois"] = whois_data
        
        # Reverse DNS
        print("\n3. Reverse DNS...")
        reverse_data = reverse_dns(ip)
        analysis_results["analysis"]["reverse_dns"] = reverse_data
        
        # Geolocation
        print("\n4. Geolocation...")
        geo_data = get_ip_geolocation(ip)
        analysis_results["analysis"]["geolocation"] = geo_data
        
        # Reputation check
        print("\n5. Reputation Check...")
        reputation_data = get_ip_reputation(ip)
        analysis_results["analysis"]["reputation"] = reputation_data
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 Analysis Summary:")
        
        if basic_lookup.get("success"):
            print(f"   Hostname: {basic_lookup.get('hostname', 'Unknown')}")
        
        if whois_data.get("success"):
            network = whois_data.get("network", {})
            print(f"   Network: {network.get('name', 'Unknown')} ({network.get('cidr', 'Unknown')})")
            print(f"   Country: {network.get('country', 'Unknown')}")
        
        if geo_data.get("success"):
            geo = geo_data.get("geolocation", {})
            print(f"   Location: {geo.get('city', 'Unknown')}, {geo.get('country', 'Unknown')}")
            print(f"   ISP: {geo.get('isp', 'Unknown')}")
        
        analysis_results["success"] = True
        return analysis_results
        
    except Exception as e:
        error_msg = f"Comprehensive analysis failed for {ip}: {str(e)}"
        logger.error(error_msg)
        print(f"❌ {error_msg}")
        return {"error": error_msg, "ip": ip, "success": False}

def subnet_analyzer(cidr: str) -> Dict[str, Any]:
    """
    Analyze a subnet/CIDR block.
    
    Args:
        cidr: CIDR notation (e.g., "***********/24")
    
    Returns:
        Dictionary containing subnet analysis
    """
    try:
        import ipaddress
        
        logger.info(f"Analyzing subnet: {cidr}")
        
        network = ipaddress.ip_network(cidr, strict=False)
        
        result = {
            "cidr": str(network),
            "network_address": str(network.network_address),
            "broadcast_address": str(network.broadcast_address),
            "netmask": str(network.netmask),
            "hostmask": str(network.hostmask),
            "num_addresses": network.num_addresses,
            "num_hosts": network.num_addresses - 2 if network.num_addresses > 2 else 0,
            "is_private": network.is_private,
            "is_multicast": network.is_multicast,
            "is_reserved": network.is_reserved,
            "timestamp": datetime.now().isoformat(),
            "success": True
        }
        
        print(f"🌐 Subnet Analysis for {cidr}:")
        print(f"   Network: {result['network_address']}")
        print(f"   Broadcast: {result['broadcast_address']}")
        print(f"   Netmask: {result['netmask']}")
        print(f"   Total Addresses: {result['num_addresses']}")
        print(f"   Usable Hosts: {result['num_hosts']}")
        print(f"   Private Network: {result['is_private']}")
        
        return result
        
    except ValueError as e:
        error_msg = f"Invalid CIDR notation: {cidr} - {str(e)}"
        logger.error(error_msg)
        print(f"❌ {error_msg}")
        return {"error": error_msg, "cidr": cidr, "success": False}
        
    except Exception as e:
        error_msg = f"Subnet analysis failed for {cidr}: {str(e)}"
        logger.error(error_msg)
        print(f"❌ {error_msg}")
        return {"error": error_msg, "cidr": cidr, "success": False}
